<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Results Template -->
        <template id="recommendation_result_template" name="Recommendation Results">
            <div class="recommendation-results">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th class="fw-bold text-center">Section</th>
                                <th class="fw-bold text-center">Details</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Recommendation Section -->
                            <tr>
                                <td class="fw-bold" style="width: 20%;">Recommendation</td>
                                <td>
                                    <t t-if="result_data.get('recommendation')">
                                        <div class="mb-2">
                                            <span t-esc="result_data.get('recommendation')"></span>
                                        </div>
                                    </t>
                                    <!-- Show recommendation attachments (names only) -->
                                     <t t-if="result_data.get('attachments', {}).get('recommendation_docs')">
                                        <div class="mt-3">
                                            <strong>Recommendation Document:</strong>
                                            <ul class="list-unstyled mt-2">
                                                <t
                                                    t-foreach="result_data.get('attachments', {}).get('recommendation_docs', [])"
                                                    t-as="doc">
                                                    <li class="mb-2">
                                                        <a t-att-href="doc.get('url', '#')" target="_blank"
                                                            class="text-primary text-decoration-none">
                                                            <i class="fa fa-download me-2"></i>
                                                            <span t-esc="doc.get('name', 'Document')"></span>
                                                        </a>
                                                        <t t-if="doc.get('file_size')">
                                                            <small class="text-muted ms-2"> (<span
                                                                    t-esc="'%.1f KB' % (doc.get('file_size', 0) / 1024)"></span>
                                                                )
                                                            </small>
                                                        </t>
                                                    </li>
                                                </t>
                                            </ul>
                                        </div>
                                    </t>
                                    <t
                                        t-if="not result_data.get('recommendation') and not result_data.get('attachments', {}).get('recommendation_docs')">
                                        <span class="text-muted">No recommendation data available</span>
                                    </t>
                                </td>
                            </tr>

                            <!-- Stakeholders Section -->
                            <tr>
                                <td class="fw-bold">Stakeholders 
<p>(general + roles)</p></td>
                                <td>
                                    <t t-if="result_data.get('stakeholders_involved') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('stakeholders_involved').get('stakeholders_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('stakeholders_involved').get('selected_stakeholders')"
                                                t-as="stakeholder">
                                                <span t-esc="stakeholder"></span>
                                            </p>

                                        </div>
                                    </t>
                                    <t t-if="result_data.get('stakeholders') != 'No data available'">
                                        <div class="mb-2">
                                            <p class="mb-0 fw-bold">Who will implement specific parts of the recommendation</p>
                                            <span t-esc="result_data.get('stakeholders').get('implementation_stakeholder')"></span>
                                            
                                            <p class="mb-0 fw-bold">Who takes long-term ownership for success/failure? </p>
                                            <span t-esc="result_data.get('stakeholders').get('ownership_stakeholder')"></span>
                                            
                                            <p class="mb-0 fw-bold">Who will track progress and performance? </p>
                                            <span t-esc="result_data.get('stakeholders').get('progress_tracker')"></span>
                                            
                                            <p class="mb-0 fw-bold"> Who gathers and responds to public feedback? </p>
                                            <span t-esc="result_data.get('stakeholders').get('feedback_handler')"></span>
                                            
                                            <p class="mb-0 fw-bold">Who decides if the intended outcomes were achieved</p>
                                            <span t-esc="result_data.get('stakeholders').get('outcome_evaluator')"></span>

                                        </div>
                                    </t>
                                    <t
                                        t-if="result_data.get('stakeholders_involved') == 'No data available' and result_data.get('stakeholders') == 'No data available'">
                                        <span class="text-muted">No stakeholders data available</span>
                                    </t>
                                </td>
                            </tr>

                            <!-- Governance Rules Section -->
                            <tr>
                                <td class="fw-bold">Governance Rules</td>
                                <td>
                                    
                                    <t t-if="result_data.get('governance_rules') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('governance_rules').get('governance_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('governance_rules').get('selected_governance')"
                                                t-as="governance">
                                                <span t-esc="governance"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Identified Challenges Section -->
                            <tr>
                                <td class="fw-bold">Identified Challenges</td>
                                <td>
                                    
                                    <t t-if="result_data.get('identified_challenges') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('identified_challenges').get('challenges_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('identified_challenges').get('selected_challenges')"
                                                t-as="challenge">
                                                <span t-esc="challenge"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Coordination Plan Section -->
                            <tr>
                                <td class="fw-bold">Coordination Plan</td>
                                <td>
                                   
                                    <t t-if="result_data.get('coordination_plan') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('coordination_plan').get('coordination_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('coordination_plan').get('selected_coordination')"
                                                t-as="plan">
                                                <span t-esc="plan"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Team Details Section -->
                            <tr>
                                <td class="fw-bold">Team Details</td>
                                <td>
                                    <t t-if="result_data.get('team_info').get('grup_motor') == 'yes'">
                                        <div class="mb-2">
                                            <p><strong>Team Leader Details:</strong></p>
                                             <p class="mb-0">Name: <span t-esc="result_data.get('team_info').get('team_leader').get('name', '')"></span></p>
                                            <p class="mb-0">Phone: <span t-esc="result_data.get('team_info', {}).get('team_leader').get('phone', '')"></span></p>
                                            <p class="mb-0">Email: <span t-esc="result_data.get('team_info', {}).get('team_leader').get('email', '')"></span></p>
                                            
                                            <p class="mt-3"><strong>Team Member Details:</strong></p>
                                            <p class="mb-0">Name: <span t-esc="result_data.get('team_info', {}).get('team_members').get('name', '')"></span></p>
                                            <p class="mb-0">Role: <span t-esc="result_data.get('team_info', {}).get('team_members').get('role', '')"></span></p>
                                            <p class="mb-0">Phone: <span t-esc="result_data.get('team_info', {}).get('team_members').get('phone', '')"></span></p>
                                            <p class="mb-0">Email: <span t-esc="result_data.get('team_info', {}).get('team_members').get('email', '')"></span></p>
                                            <p class="mb-0">Contact: <span t-esc="result_data.get('team_info', {}).get('team_members').get('contact', '')"></span></p>
                                            <p class="mb-0">Background: <span t-esc="result_data.get('team_info', {}).get('team_members').get('background', '')"></span></p>
                                            <p class="mb-0">Task Description: <span t-esc="result_data.get('team_info', {}).get('team_members').get('taskDescription', '')"></span></p>
                                        </div>
                                        <div>
                                            <p class="mt-3"><strong>Activity Timeline:</strong></p>
                                            <p class="mb-0">Activity Name: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('activityName', '')"></span></p>
                                            <p class="mb-0">Milestone: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('milestone', '')"></span></p>
                                            <p class="mb-0">Start Date: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('startDate', '')"></span></p>
                                            <p class="mb-0">End Date: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('endDate', '')"></span></p>
                                            <p class="mb-0">End Date: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('endDate', '')"></span></p>
                                            <p class="mb-0">Deliverables: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('deliverables', '')"></span></p>
                                            <p class="mb-0">Responsible Person: <span t-esc="result_data.get('team_info', ).get('activity_timeline').get('responsiblePerson', '')"></span></p>
                                            <p class="mb-0">Budget: $<span t-esc="result_data.get('team_info', ).get('activity_timeline').get('budget', '')"></span></p>
                                            
                                            
                                        </div>
                                    </t>
                                    <t t-if="result_data.get('team_info').get('grup_motor') == 'no'">
                                        <p>No team information available</p>
                                    </t>
                                     
                                </td>
                            </tr>

                            <!-- Implementation Steps Section -->
                            <tr>
                                <td class="fw-bold">Implementation Steps</td>
                                <td>
                                    
                                    <t t-if="result_data.get('implementation_steps') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('implementation_steps').get('implementation_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('implementation_steps').get('selected_implementation')"
                                                t-as="implementation">
                                                <span t-esc="implementation"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Expected Outcomes Section -->
                            <tr>
                                <td class="fw-bold">Expected Outcomes</td>
                                <td>
                                    
                                    <t t-if="result_data.get('expected_outcomes') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('expected_outcomes').get('outcomes_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('expected_outcomes').get('selected_outcomes')"
                                                t-as="outcome">
                                                <span t-esc="outcome"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Monitoring Indicators Section -->
                            <tr>
                                <td class="fw-bold">Monitoring Indicators</td>
                                <td>
                                    
                                    <t t-if="result_data.get('monitoring_indicators') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('monitoring_indicators').get('monitoring_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('monitoring_indicators').get('selected_monitoring')"
                                                t-as="monitor">
                                                <span t-esc="monitor"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Feedback Mechanisms Section -->
                            <tr>
                                <td class="fw-bold">Feedback Mechanisms</td>
                                <td>
                                    
                                     <t t-if="result_data.get('feedback_mechanisms') != 'No data available'">
                                        <div class="mb-3">
                                            <p class="mb-0"
                                                t-esc="result_data.get('feedback_mechanisms').get('feedback_text')"></p>
                                            <p class="mb-0"
                                                t-foreach="result_data.get('feedback_mechanisms').get('selected_feedback')"
                                                t-as="feedback">
                                                <span t-esc="feedback"></span>
                                            </p>

                                        </div>
                                    </t>
                                </td>
                            </tr>

                            <!-- Supporting Docs Section -->
                            <tr>
                                <td class="fw-bold">Supporting Docs</td>
                                <td>
                                    <!-- Display supporting documents data from JSON -->
                                    <t t-if="result_data.get('supporting_docs') != 'No data available'">
                                      

                                        <div class="mb-2">
                                            <p><strong>Supporting Document Details:</strong></p>
                                             <p class="mb-0">Title: <span t-esc="result_data.get('supporting_docs').get('title', '')"></span></p>
                                            <p class="mb-0">Author: <span t-esc="result_data.get('supporting_docs', {}).get('author', '')"></span></p>
                                            <p> Link:
                                                <a t-att-href="result_data.get('supporting_docs', {}).get('deliverable_link')" class="mb-0"> <span t-esc="result_data.get('supporting_docs', {}).get('deliverable_link', '')"></span></a>
                                            </p>
                                        </div>
                                    </t>

                                    <!-- Display supporting document attachments as clickable links -->
                                    <t t-if="result_data.get('attachments', {}).get('supporting_docs')">
                                        <div class="mt-3">
                                            <strong>Supporting Document Attachments:</strong>
                                            <ul class="list-unstyled mt-2">
                                                <t
                                                    t-foreach="result_data.get('attachments', {}).get('supporting_docs', [])"
                                                    t-as="doc">
                                                    <li class="mb-2">
                                                        <a t-att-href="doc.get('url', '#')" target="_blank"
                                                            class="text-primary text-decoration-none">
                                                            <i class="fa fa-download me-2"></i>
                                                            <span t-esc="doc.get('name', 'Document')"></span>
                                                        </a>
                                                        <t t-if="doc.get('file_size')">
                                                            <small class="text-muted ms-2"> (<span
                                                                    t-esc="'%.1f KB' % (doc.get('file_size', 0) / 1024)"></span>
                                                                )
                                                            </small>
                                                        </t>
                                                    </li>
                                                </t>
                                            </ul>
                                        </div>
                                    </t>

                                    <t
                                        t-if="result_data.get('supporting_docs') == 'No data available' and not result_data.get('attachments', {}).get('supporting_docs')">
                                        <span class="text-muted">No supporting documents available</span>
                                    </t>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </template>

    </data>
</odoo>