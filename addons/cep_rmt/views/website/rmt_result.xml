<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Main Template -->
        <template id="recommendation_mapping_result_page" name="Recommendation Mapping Tool Result">
            <t t-call="website.layout">
                <t t-set="head">
                    <!-- Include custom assets -->
                    <t t-call-assets="web.assets_frontend_minimal"/>
                    <t t-call-assets="cep_rmt.assets"/>
                </t>
                <div id="wrap">
                    <div class="container">
                        <!-- Header Section -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="text-center mb-4">
                                    <h1 class="display-4 text-primary">Recommendation Mapping Tool</h1>
                                    <p class="lead">
                                        The COOF-Based Recommendation Mapping Tool is an advanced
                                        decision-support
                                        system designed to align national adaptation strategies with
                                        the Context-Operations-Outcomes-Feedbacks (COOF) framework
                                        for polycentric
                                        governance. This tool enables users to systematically
                                        analyze
                                        and refine adaptation recommendations by integrating them
                                        with their respective
                                        countries' National Adaptation Plan (NAP) or other national
                                        climate policy documents.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Key Features Section -->
                        <div class="row mb-1">
                            <div class="col-12">
                                <h3 class="text-primary mb-3">Key Features:</h3>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <strong>Automated COOF Parameter Extraction:</strong> The tool processes
                                        national adaptation policy documents (PDF format) to identify key governance
                                        parameters that align with the COOF framework. </li>
                                    <li class="mb-2">
                                        <strong>Recommendation Mapping:</strong> Users input their climate adaptation
                                        recommendations, and the tool cross-references them with the relevant national
                                        policy provisions to ensure coherence with existing governance structures. </li>
                                </ul>
                            </div>
                        </div>

                        <!-- Visualization Section -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="text-center">
                                    <div id="network" class="mb-3">
                                        <img src="/cep_rmt/static/src/img/flowchart-image.png" alt="flowchart"
                                            class="img-fluid flowchart-image"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Multi-Step Recommendation Form -->
                        <div class="row mb-5">
                            <div class="col-12">

                                <div class="form-header">
                                    <div class="text-center mb-1">
                                        <h2 class="fw-bold text-primary form-header-title">Output Summery


                                        </h2>


                                    </div>


                                </div>

                                <div class="card shadow-sm form-card">
                                    <div class="card-body px-4 py-2 ">
                                        <!-- Message container for export status -->
                                        <div id="show-error-msg"></div>

                                       <div class="d-flex justify-content-end mb-3">

                                         <div class="dropdown ">
                                            <button class="btn btn-primary dropdown-toggle text-white" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                                Export
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a class="dropdown-item export-btn" href="#" data-format="pdf">
                                                        <i class="fa fa-file-pdf-o me-2"></i>PDF
                                                    </a>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item export-btn" href="#" data-format="json">
                                                        <i class="fa fa-file-code-o me-2"></i>JSON
                                                    </a>
                                                </li>
                                            </ul>
                                        </div>
                                       </div>
                                        <div id="recommendation_result" t-att-data-record-id="record.id if record else ''">

                                            <t t-call="cep_rmt.recommendation_result_template">

                                                <t t-set="result_data" t-value="result_data if result_data else {}"/>

                                            </t>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </t>
        </template>
    </data>
</odoo>