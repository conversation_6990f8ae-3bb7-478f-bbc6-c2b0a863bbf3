# Export Functionality for CEP Recommendation Mapping Tool

## Overview

The CEP Recommendation Mapping Tool now supports exporting recommendation data in two formats:
- **PDF**: Formatted document with all recommendation data and attachment links
- **JSON**: Raw data export for programmatic use

## Features

### Export Formats

1. **PDF Export**
   - Professional formatted document
   - Includes all sections from the recommendation mapping
   - Includes attachment links for easy access to supporting documents
   - Suitable for sharing and printing
   - Requires: `reportlab` package

2. **JSON Export**
   - Raw data in JSON format
   - Includes metadata (export date, record ID, etc.)
   - Suitable for data integration and analysis
   - No additional dependencies required

### How to Use

1. Navigate to the recommendation results page
2. Click the "Export" dropdown button in the top-right corner
3. Select your desired format (PDF or JSON)
4. The file will be automatically downloaded

## Installation

### Required Dependencies

For full export functionality, install the optional dependency:

```bash
pip install reportlab
```

Or install from the requirements file:

```bash
pip install -r addons/cep_rmt/requirements.txt
```

### Partial Installation

The module works without this dependency, but:
- Without `reportlab`: PDF export will show an error message
- JSON export always works (no additional dependencies)

## Technical Details

### Export Endpoints

- JSON: `/recommendation_mapping/export/json/<record_id>`
- PDF: `/recommendation_mapping/export/pdf/<record_id>`
- DOCX: `/recommendation_mapping/export/docx/<record_id>`

### Security

- All exports require user authentication
- Users can only export their own records
- Record ownership is verified before export

### Data Structure

The exported data includes all sections:
- Recommendation text
- Stakeholders information
- Governance rules
- Implementation steps
- Expected outcomes
- Monitoring indicators
- Feedback mechanisms
- Supporting documents
- Team information

## Troubleshooting

### Common Issues

1. **"PDF export not available"**
   - Install reportlab: `pip install reportlab`

2. **"DOCX export not available"**
   - Install python-docx: `pip install python-docx`

3. **Export button not working**
   - Check browser console for JavaScript errors
   - Ensure you're logged in and have access to the record

4. **File not downloading**
   - Check browser download settings
   - Ensure pop-ups are not blocked

### Browser Compatibility

The export functionality works with modern browsers that support:
- JavaScript ES6+
- Fetch API
- Blob API for file downloads

## File Naming Convention

Exported files follow this naming pattern:
- `recommendation_mapping_{record_id}.pdf`
- `recommendation_mapping_{record_id}.docx`
- `recommendation_mapping_{record_id}.json`

Where `{record_id}` is the unique identifier of the recommendation record.
