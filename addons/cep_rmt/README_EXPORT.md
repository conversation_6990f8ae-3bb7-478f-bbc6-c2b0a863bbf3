# Export Functionality for CEP Recommendation Mapping Tool

## Overview

The CEP Recommendation Mapping Tool supports exporting recommendation data in two formats:
- **PDF**: High-quality formatted document using HTML2PDF.js
- **JSON**: Raw data export for programmatic use

## Features

### Export Formats

1. **PDF Export**
   - Client-side PDF generation using HTML2PDF.js library
   - Preserves table formatting and styling from the web page
   - Includes all recommendation sections and attachment links
   - A4 format with optimized margins and page breaks
   - No server-side dependencies required
   - Works entirely in the browser

2. **JSON Export**
   - Raw data in JSON format
   - Includes metadata (export date, record ID, etc.)
   - Includes all recommendation sections and data
   - Suitable for data integration and analysis
   - No additional dependencies required

### How to Use

1. Navigate to the recommendation results page
2. Click the "Export" dropdown button in the top-right corner
3. Select your desired format (PDF or JSON)
4. The file will be automatically downloaded

## Installation

### Dependencies

No additional Python dependencies are required:
- **PDF Export**: Uses HTML2PDF.js library loaded via CDN
- **JSON Export**: Uses standard Python libraries only

The module works out of the box without any additional installations.

## Technical Details

### Export Endpoints

- JSON: `/recommendation_mapping/export/json/<record_id>`
- PDF: Client-side generation (no server endpoint required)

### Security

- All exports require user authentication
- Users can only export their own records
- Record ownership is verified before export

### Data Structure

The exported data includes all sections:
- Recommendation text
- Stakeholders information
- Governance rules
- Implementation steps
- Expected outcomes
- Monitoring indicators
- Feedback mechanisms
- Supporting documents
- Team information

## Troubleshooting

### Common Issues

1. **"HTML2PDF library not loaded"**
   - Check browser console for JavaScript errors
   - Ensure internet connection is available for CDN loading
   - Try refreshing the page

2. **Export button not working**
   - Check browser console for JavaScript errors
   - Ensure you're logged in and have access to the record

3. **File not downloading**
   - Check browser download settings
   - Ensure pop-ups are not blocked

4. **PDF formatting issues**
   - Large tables may be split across pages
   - Some CSS styles may not render perfectly in PDF
   - Try using a different browser if issues persist

### Browser Compatibility

The export functionality works with modern browsers that support:
- JavaScript ES6+
- Fetch API
- Blob API for file downloads
- HTML5 Canvas (for PDF generation)

**Recommended browsers:**
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## File Naming Convention

Exported files follow this naming pattern:
- `recommendation_mapping_{record_id}.pdf`
- `recommendation_mapping_{record_id}.json`

Where `{record_id}` is the unique identifier of the recommendation record.

## Technical Implementation

### PDF Export Process

1. **Element Capture**: The `#recommendation_result` div is cloned
2. **Styling**: PDF-specific CSS styles are applied
3. **HTML2PDF.js**: Converts the styled HTML to PDF
4. **Download**: Browser automatically downloads the generated PDF

### Configuration

The PDF export uses the following HTML2PDF.js configuration:
- **Format**: A4 portrait
- **Margins**: 10mm on all sides
- **Image Quality**: 98% JPEG
- **Scale**: 2x for high resolution
- **Page Breaks**: Automatic with table row preservation
