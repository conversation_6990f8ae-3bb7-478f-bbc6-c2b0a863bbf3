// Export functionality for Recommendation Mapping Tool Results
$(document).ready(function () {
  'use strict';

  // Initialize export functionality
  function initExportHandlers() {
    // Handle export button clicks
    $('.export-btn').off('click').on('click', function (e) {
      e.preventDefault();

      const format = $(this).data('format');
      const recordId = $('#recommendation_result').data('record-id');

      if (!recordId) {
        showMessage('Error: Record ID not found', 'error');
        return;
      }

      // Check if PDF export is available
      if (format === 'pdf' && typeof html2pdf === 'undefined') {
        showMessage('PDF export not available. HTML2PDF library not loaded.', 'error');
        return;
      }

      exportData(format, recordId);
    });
  }

  // Export data in specified format
  function exportData(format, recordId) {
    // Show loading state
    const $exportBtn = $(`.export-btn[data-format="${format}"]`);
    const originalText = $exportBtn.html();
    $exportBtn.html('<i class="fa fa-spinner fa-spin me-2"></i>Exporting...');
    $exportBtn.prop('disabled', true);

    if (format === 'pdf') {
      // Handle PDF export using HTML2PDF.js
      exportToPDF(recordId)
        .then(() => {
          showMessage('PDF export completed successfully!', 'success');
        })
        .catch(error => {
          console.error('PDF export error:', error);
          showMessage('PDF export failed: ' + error.message, 'error');
        })
        .finally(() => {
          // Restore button state
          $exportBtn.html(originalText);
          $exportBtn.prop('disabled', false);
        });
    } else if (format === 'json') {
      // Handle JSON export
      const exportUrl = `/recommendation_mapping/export/${format}/${recordId}`;

      fetch(exportUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Export failed');
        }
        return response.json();
      })
      .then(data => {
        // Create and download JSON file
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `recommendation_mapping_${recordId}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showMessage('JSON export completed successfully!', 'success');
      })
      .catch(error => {
        console.error('Export error:', error);
        showMessage('Export failed: ' + error.message, 'error');
      })
      .finally(() => {
        // Restore button state
        $exportBtn.html(originalText);
        $exportBtn.prop('disabled', false);
      });
    }
  }

  // Export to PDF using HTML2PDF.js
  function exportToPDF(recordId) {
    return new Promise((resolve, reject) => {
      try {
        // Check if html2pdf is available
        if (typeof html2pdf === 'undefined') {
          reject(new Error('HTML2PDF library not loaded'));
          return;
        }

        // Get the element to convert
        const element = document.getElementById('recommendation_result');
        if (!element) {
          reject(new Error('Recommendation result element not found'));
          return;
        }

        // Clone the element to avoid modifying the original
        const clonedElement = element.cloneNode(true);

        // Add a title to the PDF
        const title = document.createElement('h1');
        title.textContent = 'Recommendation Mapping Tool - Export';
        title.style.cssText = 'text-align: center; color: #249AFB; margin-bottom: 20px; font-size: 18px;';
        clonedElement.insertBefore(title, clonedElement.firstChild);

        // Add some styling for better PDF output
        const style = document.createElement('style');
        style.textContent = `
          .pdf-export {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #333;
            max-width: 100%;
          }
          .pdf-export .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
            page-break-inside: avoid;
          }
          .pdf-export .table th,
          .pdf-export .table td {
            border: 1px solid #ddd;
            padding: 6px 8px;
            text-align: left;
            vertical-align: top;
            word-wrap: break-word;
            max-width: 300px;
          }
          .pdf-export .table th {
            background-color: #249AFB !important;
            color: white !important;
            font-weight: bold;
            page-break-after: avoid;
          }
          .pdf-export .table td.fw-bold {
            background-color: #f8f9fa !important;
            font-weight: bold;
            width: 20%;
          }
          .pdf-export .table tr {
            page-break-inside: avoid;
          }
          .pdf-export a {
            color: #007bff !important;
            text-decoration: underline;
          }
          .pdf-export .text-primary {
            color: #007bff !important;
          }
          .pdf-export .text-muted {
            color: #6c757d !important;
          }
          .pdf-export .fa {
            display: none;
          }
          .pdf-export .btn {
            display: none;
          }
          .pdf-export .dropdown {
            display: none;
          }
        `;

        // Add the style and class to cloned element
        clonedElement.classList.add('pdf-export');
        clonedElement.insertBefore(style, clonedElement.firstChild);

        // Configure HTML2PDF options
        const opt = {
          margin: [10, 10, 10, 10],
          filename: `recommendation_mapping_${recordId}.pdf`,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            scrollX: 0,
            scrollY: 0,
            width: element.scrollWidth,
            height: element.scrollHeight
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait',
            compress: true
          },
          pagebreak: {
            mode: ['avoid-all', 'css', 'legacy'],
            before: '.page-break-before',
            after: '.page-break-after',
            avoid: '.page-break-avoid'
          }
        };

        // Generate PDF
        html2pdf()
          .set(opt)
          .from(clonedElement)
          .save()
          .then(() => {
            resolve();
          })
          .catch((error) => {
            reject(error);
          });

      } catch (error) {
        reject(error);
      }
    });
  }

  // Show message function (reuse from form.js if available)
  function showMessage(message, type = 'success') {
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    const alertClass = `alert-${type === 'success' ? 'success' : 'danger'}`;
    const timeout = type === 'success' ? 5000 : 10000;

    const alertHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        <i class="fa ${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;

    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert to the top of the page
    if ($('#show-error-msg').length) {
      $('#show-error-msg').prepend(alertHtml);
    } else {
      $('.container').first().prepend(alertHtml);
    }

    // Auto-hide after timeout
    setTimeout(() => $('.alert').fadeOut(), timeout);
  }

  // Initialize when DOM is ready
  initExportHandlers();
  
  // Re-initialize if content is dynamically loaded
  $(document).on('DOMNodeInserted', function() {
    initExportHandlers();
  });
});
