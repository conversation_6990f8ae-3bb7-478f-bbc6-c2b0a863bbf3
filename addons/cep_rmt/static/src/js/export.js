// Export functionality for Recommendation Mapping Tool Results
$(document).ready(function () {
  'use strict';

  // Initialize export functionality
  function initExportHandlers() {
    // Handle export button clicks
    $('.export-btn').off('click').on('click', function (e) {
      e.preventDefault();
      
      const format = $(this).data('format');
      const recordId = $('#recommendation_result').data('record-id');
      
      if (!recordId) {
        showMessage('Error: Record ID not found', 'error');
        return;
      }
      
      exportData(format, recordId);
    });
  }

  // Export data in specified format
  function exportData(format, recordId) {
    // Show loading state
    const $exportBtn = $(`.export-btn[data-format="${format}"]`);
    const originalText = $exportBtn.html();
    $exportBtn.html('<i class="fa fa-spinner fa-spin me-2"></i>Exporting...');
    $exportBtn.prop('disabled', true);

    // Prepare export URL
    const exportUrl = `/recommendation_mapping/export/${format}/${recordId}`;
    
    if (format === 'json') {
      // For JSON, fetch and download as file
      fetch(exportUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      })
      .then(response => {
        if (!response.ok) {
          throw new Error('Export failed');
        }
        return response.json();
      })
      .then(data => {
        // Create and download JSON file
        const jsonString = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = `recommendation_mapping_${recordId}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
        showMessage('JSON export completed successfully!', 'success');
      })
      .catch(error => {
        console.error('Export error:', error);
        showMessage('Export failed: ' + error.message, 'error');
      })
      .finally(() => {
        // Restore button state
        $exportBtn.html(originalText);
        $exportBtn.prop('disabled', false);
      });
    } else {
      // For PDF and DOCX, open in new window/download directly
      const downloadLink = document.createElement('a');
      downloadLink.href = exportUrl;
      downloadLink.target = '_blank';
      downloadLink.download = `recommendation_mapping_${recordId}.${format}`;
      
      // Add to DOM, click, and remove
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      
      // Show success message after a short delay
      setTimeout(() => {
        showMessage(`${format.toUpperCase()} export initiated successfully!`, 'success');
        
        // Restore button state
        $exportBtn.html(originalText);
        $exportBtn.prop('disabled', false);
      }, 1000);
    }
  }

  // Show message function (reuse from form.js if available)
  function showMessage(message, type = 'success') {
    const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';
    const alertClass = `alert-${type === 'success' ? 'success' : 'danger'}`;
    const timeout = type === 'success' ? 5000 : 10000;

    const alertHtml = `
      <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
        <i class="fa ${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
      </div>
    `;

    // Remove existing alerts
    $('.alert').remove();
    
    // Add new alert to the top of the page
    if ($('#show-error-msg').length) {
      $('#show-error-msg').prepend(alertHtml);
    } else {
      $('.container').first().prepend(alertHtml);
    }

    // Auto-hide after timeout
    setTimeout(() => $('.alert').fadeOut(), timeout);
  }

  // Initialize when DOM is ready
  initExportHandlers();
  
  // Re-initialize if content is dynamically loaded
  $(document).on('DOMNodeInserted', function() {
    initExportHandlers();
  });
});
