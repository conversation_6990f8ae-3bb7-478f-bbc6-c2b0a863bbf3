from odoo import models, fields, api
import json
import logging

_logger = logging.getLogger(__name__)

class RecommendationData(models.Model):
    _name = 'cep_rmt.recommendation_data'
    _description = 'Recommendation Mapping Data'
    _order = 'create_date desc'

    # Basic fields
    title = fields.Char(string='Title', required=True)
    user_id = fields.Many2one('res.users', string='User', required=True, default=lambda self: self.env.user)
    output_result_count = fields.Integer(string='Output Result Count', default=3)
    
    # Step selection field
    step = fields.Selection([
        ('recommendation', 'Recommendation'),
        ('stakeholders_involved', 'Stakeholders Involved'),
        ('stakeholders', 'Stakeholders'),
        ('governance_rules', 'Governance Rules'),
        ('identified_challenges', 'Identified Challenges'),
        ('coordination_plan', 'Coordination Plan'),
        ('team_info', 'Team Info'),
        ('implementation_steps', 'Implementation Steps'),
        ('expected_outcomes', 'Expected Outcomes'),
        ('monitoring_indicators', 'Monitoring Indicators'),
        ('feedback_mechanisms', 'Feedback Mechanisms'),
        ('supporting_docs', 'Supporting Docs'),
    ], string='Step', required=True)
    step_number = fields.Integer(string='Step Number', compute='_compute_step_number', store=True)
    # Status field
    status = fields.Selection([
        ('running', 'Running'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='running')
    
    # Error message field
    error_message = fields.Text(string='Error Message')
    
    # JSON fields for all data types
    recommendation = fields.Text(string='Recommendation Data')
    stakeholders_involved = fields.Text(string='Stakeholders Involved Data')
    stakeholders = fields.Text(string='Stakeholders Data')
    governance_rules = fields.Text(string='Governance Rules Data')
    identified_challenges = fields.Text(string='Identified Challenges Data')
    coordination_plan = fields.Text(string='Coordination Plan Data')
    team_info = fields.Text(string='Team Info Data')
    implementation_steps = fields.Text(string='Implementation Steps Data')
    expected_outcomes = fields.Text(string='Expected Outcomes Data')
    monitoring_indicators = fields.Text(string='Monitoring Indicators Data')
    feedback_mechanisms = fields.Text(string='Feedback Mechanisms Data')
    supporting_docs = fields.Text(string='Supporting Docs Data')
   
    suggestions = fields.Json(string='Suggestions Data')
 
    

    attachment_ids = fields.Many2many(
        'ir.attachment', 
        'recommendation_attachment_rel', 
        'recommendation_id', 
        'attachment_id', 
        string='Recommendation Attachments'
    )

    supporting_docs_attachment_ids = fields.Many2many(
        'ir.attachment', 
        'recommendation_supporting_docs_attachment_rel', 
        'recommendation_id', 
        'attachment_id', 
        string='Supporting Documents Attachments'
    )

    # save each stakeholders_involved, stakeholders, governance_rules, identified_challenges, coordination_plan, team_info, implementation_steps, expected_outcomes, monitoring_indicators, feedback_mechanisms, supporting_docs as json data 
    def write(self, vals_list):
        # Serialize the JSON fields if they are Python dicts
        json_fields = [
            'stakeholders_involved', 'stakeholders', 'governance_rules', 'identified_challenges',
            'coordination_plan', 'team_info', 'implementation_steps', 'expected_outcomes', 'monitoring_indicators', 'feedback_mechanisms', 'supporting_docs'
        ]
        for field in json_fields:
            if field in vals_list and isinstance(vals_list[field], dict):
                vals_list[field] = json.dumps(vals_list[field])
        
        return super(RecommendationData, self).write(vals_list)


    def save_suggestions(self,  body):
        try:
            result = body.get("results")
            project_id = body.get('project_id')
            record = self.env['cep_rmt.recommendation_data'].browse(int(project_id)).sudo()

            record.write({
                'status': 'completed',
                'step': 'stakeholders_involved',
                'suggestions': result
                })
        except Exception as e:
            _logger.error(f"Error saving agendas: {str(e.with_traceback(None))}")
    
  
   

    @api.depends('step')
    def _compute_step_number(self):
        step_order = {
            'recommendation': 1,
            'stakeholders_involved': 2,
            'stakeholders': 3,
            'governance_rules': 4,
            'identified_challenges': 5,
            'coordination_plan': 6,
            'team_info': 7,
            'implementation_steps': 8,
            'expected_outcomes': 9,
            'monitoring_indicators': 10,
            'feedback_mechanisms': 11,
            'supporting_docs': 12,
        }
        for record in self:
            record.step_number = step_order.get(record.step, 0)