from odoo import http
from odoo.http import request
import json
import base64
import logging
import json
_logger = logging.getLogger(__name__)


def isAuthenticate():
    """Check if user is authenticated"""
    # For JSON endpoints, check if user is not the public user
    try:
        # Check if user has a real login (not public user)
        return (
            request.env.user.login
            and request.env.user.login != "public"
            and request.env.user.id != 4
        )
    except:
        # Fallback: assume not authenticated
        return False


def isRecordOwner(record, user_id=None):
    """Check if the current user owns the record"""
    if user_id is None:
        user_id = request.env.user.id
    return record.user_id.id == user_id if record else False


class RecommendationMappingController(http.Controller):

    _root_url = "/recommendation_mapping"

    @http.route(f"{_root_url}", type="http", auth="user", website=True)
    def recommendation_mapping_page(self, **kwargs):
        """Render the recommendation mapping tool page"""
        # Check authentication
        if not isAuthenticate():
            return request.redirect("/web/login")

        return request.render("cep_rmt.recommendation_mapping_page")

    @http.route(
        f"{_root_url}/api/process",
        type="json",
        auth="user",
        methods=["POST"],
        csrf=False,
    )
    def process_recommendations_api(self, **kwargs):
        """
        API endpoint to process recommendations with RabbitMQ integration
        Requires authentication and handles user ownership
        """
        try:

            data = json.loads(request.httprequest.data)

            recommendation_text = data.get("recommendation_text", "")
            file_data = data.get("file_data", "")
            file_name = data.get("file_name", "")
            output_result_count = int(data.get("output_result_count", 3))

            # Validate inputs
            if not recommendation_text.strip():
                return {
                    "success": False,
                    "error": "Recommendation text is required",
                    "error_code": "MISSING_RECOMMENDATION",
                }

            if not file_data:
                return {
                    "success": False,
                    "error": "Attachment file is required",
                    "error_code": "MISSING_FILE",
                }

            if output_result_count < 1 or output_result_count > 10:
                return {
                    "success": False,
                    "error": "Output result count must be between 1 and 10",
                    "error_code": "INVALID_COUNT",
                }

            # Create recommendation record with user ownership
            recommendation_model = request.env["cep_rmt.recommendation_data"]

            data = {
                "title": f"{recommendation_text[:50]}",
                "step": "recommendation",
                "status": "running",
                "user_id": request.env.user.id,
                "recommendation": recommendation_text,  # Set the JSON data for the specific step field
            }

            record = recommendation_model.create(data)

            # Handle file attachment
            attachment_ids = []
            attachment_id = None
            if file_data and file_name:
                try:
                    # Validate file type (PDF only for NAP files)
                    if not file_name.lower().endswith(".pdf"):
                        return {
                            "success": False,
                            "error": "Only PDF files are allowed for NAP documents",
                            "error_code": "INVALID_FILE_TYPE",
                        }

                    # Validate file size (max 10MB)
                    try:
                        file_content = base64.b64decode(file_data)
                        file_size = len(file_content)
                        max_size = 10 * 1024 * 1024  # 10MB

                        if file_size > max_size:
                            return {
                                "success": False,
                                "error": "File size must be less than 10MB",
                                "error_code": "FILE_TOO_LARGE",
                            }
                    except Exception:
                        return {
                            "success": False,
                            "error": "Invalid file data",
                            "error_code": "INVALID_FILE_DATA",
                        }

                    # Create attachment
                    attachment = (
                        request.env["ir.attachment"]
                        .sudo()
                        .create(
                            {
                                "name": file_name,
                                "datas": file_data,
                                "res_model": "cep_rmt.recommendation_data",
                                "res_id": record.id,
                                "type": "binary",
                                "mimetype": "application/pdf",
                                "public": True,
                            }
                        )
                    )
                    attachment_ids.append(attachment.id)
                    attachment_id = attachment.id

                    if attachment_ids:
                        record.write({'attachment_ids': [(6, 0, attachment_ids)]})

                except Exception as e:
                    _logger.error(f"Error creating attachment: {str(e)}")
                    return {
                        "success": False,
                        "error": "Failed to process attachment",
                        "error_code": "ATTACHMENT_ERROR",
                    }

            # Send RabbitMQ message
            try:
                rabbitmq_server = (
                    request.env["rabbitmq.server"].sudo().search([], limit=1)
                )

                if not rabbitmq_server:
                    return {
                        "success": False,
                        "error": "Message queue service not available",
                        "error_code": "QUEUE_NOT_FOUND",
                    }

                # base_url = "http://host.docker.internal:8069"
                base_url = (
                    request.env["ir.config_parameter"].sudo().get_param("web.base.url")
                )

                # Prepare message payload
                message_payload = {
                    "event": "PROCESS_RECOMMENDATIONS",
                    "project_id": record.id,
                    "user_id": request.env.user.id,
                    "user_name": request.env.user.name,
                    "recommendation_text": recommendation_text,
                    "output_result_count": output_result_count,
                    "file_name": file_name,
                    "attachment": f"{base_url}/web/content/{attachment_id}?download=true",
                }

                # Publish message
                rabbitmq_server.publish_message(message_payload, "RMT_SERVICE")

                return {
                    "success": True,
                    "message": "Recommendation processing started successfully",
                    "record_id": record.id,
                    "step": record.step_number,
                    "status": "running",
                }

            except Exception as e:
                # Update record status to failed
                record.write(
                    {
                        "status": "failed",
                        "error_message": f"Failed to send processing request",
                    }
                )
                return {
                    "success": False,
                    "error": "Failed to start processing",
                    "error_code": "QUEUE_ERROR",
                }

        except Exception as e:
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }

    @http.route(
        f"{_root_url}/api/suggestions/<string:id>",
        type="http",
        auth="user",
        methods=["GET"],
        csrf=False,
    )
    def get_suggestions_data_api(self, id):
        """API endpoint to get recommendation data"""
        try:
            if not id:
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Record ID is required",
                            "error_code": "MISSING_RECORD_ID",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=400,
                )

            record = request.env["cep_rmt.recommendation_data"].browse(int(id)).sudo()

            if not record.exists():
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Record not found",
                            "error_code": "RECORD_NOT_FOUND",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=404,
                )

            # Check if user owns the record
            if not isRecordOwner(record):
                return request.make_response(
                    json.dumps(
                        {
                            "success": False,
                            "error": "Access denied",
                            "error_code": "ACCESS_DENIED",
                        }
                    ),
                    headers=[("Content-Type", "application/json")],
                    status=403,
                )

            return request.make_response(
                json.dumps(
                    {
                        "success": True,
                        "status": record.status,
                        "step": record.step_number,
                        "record_id": record.id,
                        "data": record.suggestions,
                    }
                ),
                headers=[("Content-Type", "application/json")],
            )

        except Exception as e:
            _logger.error(f"Error in get_suggestions_data_api: {str(e)}")
            return request.make_response(
                json.dumps(
                    {
                        "success": False,
                        "error": "An unexpected error occurred",
                        "error_code": "INTERNAL_ERROR",
                    }
                ),
                headers=[("Content-Type", "application/json")],
                status=500,
            )

   
    @http.route(
        f"{_root_url}/api/step-data",
        type="json",
        auth="user",
        methods=["POST"],
        csrf=False,
    )
    def submit_step_data_api(self, **kwargs):
        """
        Common API endpoint for steps 2-11 data submission
        """
        try:
            data = json.loads(request.httprequest.data)

            step_number = data.get("step_number")
            step_name = data.get("step_name")
            input_data = data.get("input_data", {})
            record_id = data.get("record_id")
            _logger.error(f"Dipro")
            _logger.error(f"input_data: {type(input_data)}")
            # Validate inputs
            if not step_number or not step_name:
                return {
                    "success": False,
                    "error": "Step number and step name are required",
                    "error_code": "MISSING_STEP_INFO",
                }

            if not record_id:
                return {
                    "success": False,
                    "error": "Record ID is required",
                    "error_code": "MISSING_RECORD_ID",
                }

            # Get the record
            record = request.env["cep_rmt.recommendation_data"].browse(int(record_id))

            if not record.exists():
                return {
                    "success": False,
                    "error": "Record not found",
                    "error_code": "RECORD_NOT_FOUND",
                }

            # Check if user owns the record
            if not isRecordOwner(record):
                return {
                    "success": False,
                    "error": "Access denied",
                    "error_code": "ACCESS_DENIED",
                }

            # Get the next step name
            step_order = [
                'recommendation', 'stakeholders_involved', 'stakeholders', 'governance_rules',
                'identified_challenges', 'coordination_plan', 'team_info', 'implementation_steps',
                'expected_outcomes', 'monitoring_indicators', 'feedback_mechanisms', 'supporting_docs'
            ]

            try:
                current_index = step_order.index(step_name)
                next_step_name = step_order[current_index + 1] if current_index + 1 < len(step_order) else step_name
            except ValueError:
                next_step_name = step_name  # Fallback if step_name not found

            # Update the record with step data and next step
            update_data = {
                "step": next_step_name,
            }

            # Map step data to the appropriate field
            step_field_mapping = {
                "stakeholders_involved": "stakeholders_involved",
                "stakeholders": "stakeholders",
                "governance_rules": "governance_rules",
                "identified_challenges": "identified_challenges",
                "coordination_plan": "coordination_plan",
                "team_info": "team_info",
                "implementation_steps": "implementation_steps",
                "expected_outcomes": "expected_outcomes",
                "monitoring_indicators": "monitoring_indicators",
                "feedback_mechanisms": "feedback_mechanisms",
                "supporting_docs": "final_details",
            }

            if step_name in step_field_mapping:
                field_name = step_field_mapping[step_name]
                update_data[field_name] = input_data

            # Update the record
            record.write(update_data)

            return {
                "success": True,
                "message": f"Step {step_number} data saved successfully",
                "record_id": record.id,
                "step": record.step_number,
            }

        except Exception as e:
            _logger.error(f"Error in submit_step_data_api: {str(e)}")
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }

   
  
    @http.route(
        f"{_root_url}/api/final-submission",
        type="json",
        auth="user",
        methods=["POST"],
        csrf=False,
    )
    def submit_final_data_api(self, **kwargs):
        """
        API endpoint for final step (step 12) data submission
        """
        try:
            data = json.loads(request.httprequest.data)

            record_id = data.get("record_id")
            final_data = data.get("final_data", {})

            # Validate inputs
            if not record_id:
                return {
                    "success": False,
                    "error": "Record ID is required",
                    "error_code": "MISSING_RECORD_ID",
                }

            # Get the record
            record = request.env["cep_rmt.recommendation_data"].browse(int(record_id))

            if not record.exists():
                return {
                    "success": False,
                    "error": "Record not found",
                    "error_code": "RECORD_NOT_FOUND",
                }

            # Check if user owns the record
            if not isRecordOwner(record):
                return {
                    "success": False,
                    "error": "Access denied",
                    "error_code": "ACCESS_DENIED",
                }

           

            # Handle supporting documents if provided
            supporting_attachment_ids = []
            supporting_docs = final_data.get("supporting_docs", [])
            for doc in supporting_docs:
                if doc.get("data") and doc.get("name"):
                    try:
                        # Create attachment
                        attachment = request.env["ir.attachment"].sudo().create(
                            {
                                "name": doc["name"],
                                "datas": doc["data"],
                                "res_model": "cep_rmt.recommendation_data",
                                "res_id": record.id,
                                "type": "binary",
                                "mimetype": doc.get("type", "application/octet-stream"),
                                "public": True,
                            }
                        )
                        supporting_attachment_ids.append(attachment.id)
                    except Exception as e:
                        _logger.error(f"Error creating attachment: {str(e)}")

            final_data.pop("supporting_docs", None)

            update_data = {
                "step": "supporting_docs",
                "supporting_docs": final_data,

            }
            if supporting_attachment_ids:
                update_data['supporting_docs_attachment_ids'] = [(6, 0, supporting_attachment_ids)]  # Replace all with new list


            # Update the record
            record.write(update_data)

            return {
                "success": True,
                "message": "Final data saved successfully",
                "record_id": record.id,
            }

        except Exception as e:
            _logger.error(f"Error in submit_final_data_api: {str(e)}")
            return {
                "success": False,
                "error": "An unexpected error occurred",
                "error_code": "INTERNAL_ERROR",
            }
    # result page
    @http.route(
        f"{_root_url}/result/<string:id>",
        type="http",
        auth="user",
        methods=["GET"],
        website=True,
    )
    def result_page(self, id, **kwargs):
        if not isAuthenticate():
            return request.redirect('/web/login')

        try:
            # Get the record
            record = request.env["cep_rmt.recommendation_data"].browse(int(id))

            if not record.exists():
                return request.not_found()

            # Check if user owns the record
            if not isRecordOwner(record):
                return request.redirect('/web/login')

            # Prepare data for template
            result_data = self._prepare_result_data(record)

            return request.render('cep_rmt.recommendation_mapping_result_page', {
                'record': record,
                'result_data': result_data,
            })

        except Exception as e:
            _logger.error(f"Error in result_page: {str(e)}")
            return request.render('website.404')

    def _safe_get_json_data(self, field_value):
        """Get JSON data as actual data structure (dict/list) or plain string"""
        _logger.error(f"field_value: {type(field_value)}")
        if not field_value:
            return "No data available"

        if isinstance(field_value, dict):
            _logger.error(f"field_value: {field_value}")
            _logger.error(f"field_value: {type(field_value)}")
            # Return the dict as-is
            return field_value

        if isinstance(field_value, str):
            try:
                parsed = json.loads(field_value)
                # Return parsed data structure (could be dict, list, etc.)
                _logger.error(f"Parsed: {parsed}")
                _logger.error(f"Parsed: {type(parsed)}")
                return parsed
            except:
                # If parsing fails, return the original string
                return field_value

        return field_value
    def _prepare_result_data(self, record):
        """Prepare and format result data from JSON fields"""
        try:
            # # Helper function to safely get JSON data and convert to readable string

            result_data = {
                'recommendation': record.recommendation,
                'stakeholders_involved': self._safe_get_json_data(record.stakeholders_involved),
                'stakeholders': self._safe_get_json_data(record.stakeholders),
                'governance_rules': self._safe_get_json_data(record.governance_rules),
                'identified_challenges': self._safe_get_json_data(record.identified_challenges),
                'coordination_plan': self._safe_get_json_data(record.coordination_plan),
                'team_info': self._safe_get_json_data(record.team_info),
                'implementation_steps': self._safe_get_json_data(record.implementation_steps),
                'expected_outcomes': self._safe_get_json_data(record.expected_outcomes),
                'monitoring_indicators': self._safe_get_json_data(record.monitoring_indicators),
                'feedback_mechanisms': self._safe_get_json_data(record.feedback_mechanisms),
                'supporting_docs': self._safe_get_json_data(record.supporting_docs),
                'attachments': self._get_attachments_data(record)
            }
         
            return result_data

        except Exception as e:
            _logger.error(f"Error preparing result data: {str(e)}")
            return {}

    def _get_attachments_data(self, record):
        """Get attachment data for supporting documents and recommendations"""
        try:
            attachments = {
                'supporting_docs': [],
                'recommendation_docs': []
            }

            base_url = request.env["ir.config_parameter"].sudo().get_param("web.base.url")

            # Get recommendation documents by attachment_ids
            if hasattr(record, 'attachment_ids') and record.attachment_ids:
                for attachment in record.attachment_ids:
                    attachment_data = {
                        'id': attachment.id,
                        'name': attachment.name,
                        'url': f"{base_url}/web/content/{attachment.id}?download=true",
                        'mimetype': attachment.mimetype,
                        'file_size': attachment.file_size,
                        'create_date': attachment.create_date.strftime('%Y-%m-%d %H:%M:%S') if attachment.create_date else '',
                    }
                    attachments['recommendation_docs'].append(attachment_data)
           
            # Get supporting documents by supporting_docs_attachment_ids
            if hasattr(record, 'supporting_docs_attachment_ids') and record.supporting_docs_attachment_ids:
                for attachment in record.supporting_docs_attachment_ids:
                    attachment_data = {
                        'id': attachment.id,
                        'name': attachment.name,
                        'url': f"{base_url}/web/content/{attachment.id}?download=true",
                        'mimetype': attachment.mimetype,
                        'file_size': attachment.file_size,
                        'create_date': attachment.create_date.strftime('%Y-%m-%d %H:%M:%S') if attachment.create_date else '',
                    }
                    attachments['supporting_docs'].append(attachment_data)
           
            return attachments

        except Exception as e:
            _logger.error(f"Error getting attachments data: {str(e)}")
            return {'supporting_docs': [], 'recommendation_docs': []}