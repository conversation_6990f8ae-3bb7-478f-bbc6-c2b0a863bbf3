{ 
    'name': "CEP Recommendation Mapping Tool", 
    'summary': "Recommendation Mapping Tool for Climate Adaptation", 
    'description': """TODO""", 
    'author': "Technovative Solutions LTD", 
    'license': "AGPL-3", 
    'website': "https://www.technovativesolutions.co.uk", 
    'category': 'Tools', 
    'version': '0.0.1', 
    'depends': [
        'base',
        'website',
        'rabbitmq',
        'portal'
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/website/01_form_step_one.xml',
        'views/website/02_form_step_two.xml',
        'views/website/03_form_step_three.xml',
        'views/website/04_form_step_four.xml',
        'views/website/05_form_step_five.xml',
        'views/website/06_form_step_six.xml',
        'views/website/07_form_step_seven.xml',
        'views/website/08_form_step_eight.xml',
        'views/website/09_form_step_nine.xml',
        'views/website/10_form_step_ten.xml',
        'views/website/11_form_step_eleven.xml',
        'views/website/12_form_step_twelve.xml',
        'views/website/recommendation_mapping_page.xml',
        'views/website/rmt_result_template.xml',
        'views/website/rmt_result.xml',
    ],
    'assets': {
         'cep_rmt.assets': [
            'https://code.jquery.com/jquery-3.7.1.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js',
            # 'cep_rmt/static/src/js/common/form_base.js',
            'cep_rmt/static/src/js/suggestions_handler.js',
            'cep_rmt/static/src/js/form.js',
            'cep_rmt/static/src/js/export.js',
            # 'cep_rmt/static/src/js/form_controller.js',
            'cep_rmt/static/src/css/recommendation_mapping.css',
        ],

    },
   
    'installable': True,
    'application': True,
    'auto_install': False,
    
}